import {redirect, type LoaderFunctionArgs} from '@shopify/remix-oxygen';
import {useLoaderData, type MetaFunction, Await, Link} from 'react-router';
import {
  getSelectedProductOptions,
  Analytics,
  useOptimisticVariant,
  getProductOptions,
  getAdjacentAndFirstAvailableVariants,
  useSelectedOptionInUrlParam,
} from '@shopify/hydrogen';
import {ProductPrice} from '~/components/ProductPrice';
import {ProductHeroImage, ProductThumbnailImage} from '~/components/ProductImage';
import {ProductForm} from '~/components/ProductForm';
import {ProductUpsellCrosssell} from '~/components/ProductUpsellCrosssell';
import {CrossSellProducts} from '~/components/CrossSellProducts';
import {redirectIfHandleIsLocalized} from '~/lib/redirect';
import React, {Suspense, useState} from 'react';
import {useNavigate, Link} from 'react-router';
import {ProductOptionSwatch} from '~/components/ProductOptionSwatch';
import {Money, Image} from '@shopify/hydrogen';
import {AddToCartButton} from '~/components/AddToCartButton';
import {useAside} from '~/components/Aside';

export const meta: MetaFunction<typeof loader> = ({data}) => {
  return [
    {title: `Hydrogen | ${data?.product.title ?? ''}`},
    {
      rel: 'canonical',
      href: `/products/${data?.product.handle}`,
    },
  ];
};

export async function loader(args: LoaderFunctionArgs) {
  // Start fetching non-critical data without blocking time to first byte
  const deferredData = loadDeferredData(args);

  // Await the critical data required to render initial state of the page
  const criticalData = await loadCriticalData(args);

  return {...deferredData, ...criticalData};
}

/**
 * Load data necessary for rendering content above the fold. This is the critical data
 * needed to render the page. If it's unavailable, the whole page should 400 or 500 error.
 */
async function loadCriticalData({
  context,
  params,
  request,
}: LoaderFunctionArgs) {
  const {handle} = params;
  const {storefront} = context;

  if (!handle) {
    throw new Error('Expected product handle to be defined');
  }

  const [{product}] = await Promise.all([
    storefront.query(PRODUCT_QUERY, {
      variables: {handle, selectedOptions: getSelectedProductOptions(request)},
      cache: storefront.CacheLong(), // Aggressive caching for product data
    }),
    // Add other queries here, so that they are loaded in parallel
  ]);

  if (!product?.id) {
    throw new Response(null, {status: 404});
  }

  // The API handle might be localized, so redirect to the localized handle
  redirectIfHandleIsLocalized(request, {handle, data: product});

  return {
    product,
  };
}

/**
 * Load data for rendering content below the fold. This data is deferred and will be
 * fetched after the initial page load. If it's unavailable, the page should still 200.
 * Make sure to not throw any errors here, as it will cause the page to 500.
 */
function loadDeferredData({context, params}: LoaderFunctionArgs) {
  const {storefront} = context;

  // Fetch related products for upsell/cross-sell
  const relatedProducts = storefront
    .query(RELATED_PRODUCTS_QUERY, {
      variables: {first: 20},
      cache: storefront.CacheLong(),
    })
    .catch((error) => {
      console.error('Error fetching related products:', error);
      return {products: {nodes: []}};
    });

  return {
    relatedProducts,
  };
}

export default function Product() {
  const {product, relatedProducts} = useLoaderData<typeof loader>();
  const navigate = useNavigate();

  // State for selected image
  const [selectedImageIndex, setSelectedImageIndex] = React.useState(0);

  // State for quantity and purchase options
  const [quantity, setQuantity] = useState(1);

  // Optimistically selects a variant with given available variant information
  const selectedVariant = useOptimisticVariant(
    product.selectedOrFirstAvailableVariant,
    getAdjacentAndFirstAvailableVariants(product),
  );

  // Sets the search param to the selected variant without navigation
  // only when no search params are set in the url
  useSelectedOptionInUrlParam(selectedVariant.selectedOptions);

  // Get the product options array
  const productOptions = getProductOptions({
    ...product,
    selectedOrFirstAvailableVariant: selectedVariant,
  });

  const {title, descriptionHtml} = product;

  return (
    <div className="min-h-screen product-page-content" style={{
      backgroundImage: 'url(/newhomepage/mobile_homeage_bg_sf.webp), url(/newhomepage/mobile_homeage_bg_sf.png)',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    }}>
      {/* Compact Hero Section */}
      <div className="relative bg-gradient-to-br from-[#3a5c5c]/95 to-[#2d4747]/95 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 items-start">

            {/* Compact Product Images Section */}
            <div className="space-y-2">
              {product.images?.nodes?.length > 0 ? (
                <>
                  {/* Main Product Image */}
                  <div className="relative group">
                    <div className="aspect-square bg-white/10 rounded-xl overflow-hidden backdrop-blur-sm border border-white/20 shadow-lg">
                      <ProductHeroImage
                        image={product.images.nodes[selectedImageIndex] || product.images.nodes[0]}
                      />
                    </div>

                    {/* Compact Origin Badge */}
                    <div className="absolute top-2 left-2 bg-[#db8027] text-white px-2 py-1 rounded-full text-xs font-bold">
                      Nicaragua
                    </div>

                    {/* Quality Badge */}
                    <div className="absolute top-2 right-2 bg-army-600 text-white px-2 py-1 rounded-full text-xs font-bold">
                      Premium
                    </div>
                  </div>

                  {product.images.nodes.length > 1 && (
                    <div className="flex space-x-2 overflow-x-auto">
                      {product.images.nodes.map((image, index) => (
                        <div
                          key={image.id}
                          className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border transition-all duration-200 cursor-pointer ${
                            selectedImageIndex === index
                              ? 'border-[#db8027] border-2'
                              : 'border-white/30 hover:border-white/50'
                          }`}
                          onClick={() => setSelectedImageIndex(index)}
                        >
                          <ProductThumbnailImage
                            image={image}
                            isActive={selectedImageIndex === index}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <div className="aspect-square bg-white/10 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/20">
                  <div className="text-center">
                    <svg className="w-12 h-12 text-white/60 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="text-white/60 text-sm">No image available</span>
                  </div>
                </div>
              )}
            </div>

            {/* Ultra-Compact Product Details */}
            <div className="space-y-0.5">
              {/* Minimal Breadcrumb */}
              <nav className="flex items-center space-x-1 text-xs text-white/60">
                <Link to="/collections/all" className="hover:text-[#db8027] transition-colors">Coffee</Link>
                <span>→</span>
                <span className="text-white/80">{title}</span>
              </nav>

              {/* Compact Cart Options - Full Height */}
              <div className="bg-white/95 backdrop-blur-sm rounded-lg p-3 border border-white/30 shadow-lg h-full flex flex-col">

                {/* Top Row - Title/Price + Size/Grind/Quantity */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">

                  {/* Left - Product Info & Price */}
                  <div className="space-y-2">
                    <div>
                      <p className="text-[#db8027] text-sm font-bold uppercase tracking-wide">{product.vendor}</p>
                      <h1 className="text-xl lg:text-2xl font-bold text-gray-800 leading-tight">{title}</h1>
                    </div>

                    {/* Price */}
                    <div className="text-2xl lg:text-3xl font-bold text-[#db8027]">
                      <ProductPrice
                        price={selectedVariant?.price}
                        compareAtPrice={selectedVariant?.compareAtPrice}
                      />
                    </div>
                  </div>

                  {/* Right - Size/Grind/Quantity Options Only */}
                  <div className="space-y-3">
                    <ProductForm
                      productOptions={productOptions}
                      selectedVariant={selectedVariant}
                      product={product}
                      hideProductOptions={false}
                      hideQuantitySelector={false}
                      showOnlyOptions={true}
                    />
                  </div>

                </div>

                {/* Bottom Section - Rest of ProductForm */}
                <div className="flex-1">
                  <ProductForm
                    productOptions={productOptions}
                    selectedVariant={selectedVariant}
                    product={product}
                    hideProductOptions={true}
                    hideQuantitySelector={true}
                  />
                </div>

              </div>

            </div>
          </div>
        </div>
      </div>

      {/* Streamlined Product Benefits Section */}
      <div className="bg-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">

            {/* Product Description */}
            <div className="space-y-4">
              <div>
                <h2 className="text-2xl lg:text-3xl font-bold text-[#3a5c5c] mb-2">Why You'll Love This Coffee</h2>
                <div className="w-12 h-1 bg-[#db8027] mb-3"></div>
              </div>

              <div
                className="text-gray-700 leading-relaxed text-base"
                dangerouslySetInnerHTML={{__html: descriptionHtml}}
              />

              {/* Key Features */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-4">
                <div className="flex items-center space-x-2 p-3 bg-[#eeedc1] rounded-lg">
                  <div className="w-8 h-8 bg-[#3a5c5c] rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-semibold text-[#3a5c5c] text-sm">Premium Quality</div>
                    <div className="text-xs text-gray-600">Expert roasted</div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 p-3 bg-[#eeedc1] rounded-lg">
                  <div className="w-8 h-8 bg-[#db8027] rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-semibold text-[#3a5c5c] text-sm">Nicaragua Origin</div>
                    <div className="text-xs text-gray-600">Single source</div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 p-3 bg-[#eeedc1] rounded-lg">
                  <div className="w-8 h-8 bg-[#5d8e8e] rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-semibold text-[#3a5c5c] text-sm">Quality Guarantee</div>
                    <div className="text-xs text-gray-600">Premium coffee beans</div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 p-3 bg-[#eeedc1] rounded-lg">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9.5 9.293 10.793a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-semibold text-[#3a5c5c] text-sm">Ethically Sourced</div>
                    <div className="text-xs text-gray-600">Fair trade</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Visual Element */}
            <div className="relative">
              <div className="bg-gradient-to-br from-[#3a5c5c] to-[#2d4747] rounded-2xl p-6 text-white relative overflow-hidden">
                {/* Background pattern */}
                <div className="absolute inset-0 opacity-10">
                  <div className="absolute top-0 right-0 w-24 h-24 bg-white rounded-full translate-x-12 -translate-y-12"></div>
                  <div className="absolute bottom-0 left-0 w-20 h-20 bg-white rounded-full -translate-x-10 translate-y-10"></div>
                </div>

                <div className="relative z-10 text-center">
                  <div className="text-4xl mb-3">☕</div>
                  <h3 className="text-xl font-bold mb-2">From Farm to Cup</h3>
                  <p className="text-white/80 mb-4 leading-relaxed text-sm">
                    Sourced directly from the volcanic highlands of Nicaragua, where rich soil and perfect climate create exceptional coffee.
                  </p>
                  <div className="inline-flex items-center space-x-2 bg-[#db8027] rounded-full px-3 py-1.5">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                    </svg>
                    <span className="font-semibold text-sm">Made with Love</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Visual Transition Section */}
      <div className="bg-gradient-to-b from-white to-[#eeedc1] py-6">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center space-x-3 bg-white/80 backdrop-blur-sm rounded-full px-6 py-2.5 shadow-lg">
            <div className="flex items-center space-x-1.5">
              <div className="w-2.5 h-2.5 bg-[#3a5c5c] rounded-full animate-pulse"></div>
              <div className="w-2.5 h-2.5 bg-[#db8027] rounded-full animate-pulse delay-200"></div>
              <div className="w-2.5 h-2.5 bg-[#5d8e8e] rounded-full animate-pulse delay-400"></div>
            </div>
            <span className="text-[#3a5c5c] font-semibold text-base">Ready to save even more?</span>
            <div className="flex items-center space-x-1.5">
              <div className="w-2.5 h-2.5 bg-[#5d8e8e] rounded-full animate-pulse delay-600"></div>
              <div className="w-2.5 h-2.5 bg-[#db8027] rounded-full animate-pulse delay-800"></div>
              <div className="w-2.5 h-2.5 bg-[#3a5c5c] rounded-full animate-pulse delay-1000"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Bundle Upsell Section */}
      <div className="bg-gradient-to-r from-[#db8027] to-[#c4721f] py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-white mb-8">
            <h2 className="text-3xl lg:text-4xl font-bold mb-3">🎁 Upgrade to Bundles & Save Big!</h2>
            <p className="text-lg lg:text-xl opacity-90 mb-3">Get more variety, better value, and never run out of great coffee</p>
            <div className="inline-flex items-center space-x-2 bg-white/20 rounded-full px-4 py-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
              </svg>
              <span className="text-base font-bold">Save up to 25% with bundles + subscription</span>
            </div>
          </div>

          <Suspense fallback={
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 animate-pulse">
                <div className="h-20 bg-white/20 rounded mb-4"></div>
                <div className="h-4 bg-white/20 rounded mb-2"></div>
                <div className="h-4 bg-white/20 rounded mb-4"></div>
                <div className="h-10 bg-white/20 rounded"></div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 animate-pulse">
                <div className="h-20 bg-white/20 rounded mb-4"></div>
                <div className="h-4 bg-white/20 rounded mb-2"></div>
                <div className="h-4 bg-white/20 rounded mb-4"></div>
                <div className="h-10 bg-white/20 rounded"></div>
              </div>
            </div>
          }>
            <Await resolve={relatedProducts}>
              {(resolvedProducts) => (
                <BundleDeals allProducts={resolvedProducts?.products?.nodes || []} />
              )}
            </Await>
          </Suspense>


        </div>
      </div>

      {/* Enhanced Cross-sell Section - 4 Products (K-cups + 3 Random) */}
      <div className="bg-gradient-to-br from-[#eeedc1] to-[#e5e2a8] py-12 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-6 left-6 w-32 h-32 bg-[#3a5c5c] rounded-full"></div>
          <div className="absolute bottom-6 right-6 w-24 h-24 bg-[#db8027] rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-[#5d8e8e] rounded-full"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-10">
            <div className="inline-flex items-center space-x-2 mb-4">
              <div className="w-8 h-1 bg-[#3a5c5c] rounded-full"></div>
              <span className="text-4xl">☕</span>
              <div className="w-8 h-1 bg-[#db8027] rounded-full"></div>
            </div>
            <h2 className="text-3xl lg:text-4xl font-bold text-[#3a5c5c] mb-4 text-center">You Might Also Love</h2>
            <p className="text-lg lg:text-xl text-[#3a5c5c]/80 max-w-2xl mx-auto text-center">
              Discover more amazing flavors from our carefully curated collection
            </p>
            <div className="mt-4 inline-flex items-center space-x-2 bg-white/60 backdrop-blur-sm rounded-full px-4 py-2">
              <svg className="w-4 h-4 text-[#db8027]" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
              </svg>
              <span className="text-[#3a5c5c] font-semibold text-sm">Handpicked for you</span>
            </div>
          </div>

          <Suspense fallback={
            <div className="text-center py-4">
              <div className="inline-flex items-center space-x-2 text-[#3a5c5c]">
                <svg className="animate-spin h-5 w-5" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="text-base">Loading recommendations...</span>
              </div>
            </div>
          }>
            <Await resolve={relatedProducts}>
              {(resolvedProducts) => (
                <CrossSellProducts
                  currentProduct={product}
                  allProducts={resolvedProducts?.products?.nodes || []}
                />
              )}
            </Await>
          </Suspense>
        </div>
      </div>

      {/* Final Call-to-Action Section */}
      <div className="bg-gradient-to-r from-[#3a5c5c] via-[#2d4747] to-[#3a5c5c] py-12 relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-64 h-64 bg-white/5 rounded-full -translate-x-32 -translate-y-32 animate-pulse"></div>
          <div className="absolute bottom-0 right-0 w-56 h-56 bg-white/5 rounded-full translate-x-28 translate-y-28 animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/3 w-48 h-48 bg-white/5 rounded-full animate-pulse delay-500"></div>
        </div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <div className="text-white space-y-6">
            <div className="text-5xl mb-4">🌟</div>
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-center">
              Ready to Start Your Coffee Journey?
            </h2>
            <p className="text-lg lg:text-xl text-white/90 mb-6 max-w-2xl mx-auto leading-relaxed text-center">
              Join thousands of coffee lovers who've discovered their perfect cup with Big River Coffee
            </p>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <div className="text-2xl font-bold text-[#db8027] mb-1">10,000+</div>
                <div className="text-white/80 text-sm">Happy Customers</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <div className="text-2xl font-bold text-[#db8027] mb-1">25%</div>
                <div className="text-white/80 text-sm">Max Savings</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                <div className="text-2xl font-bold text-[#db8027] mb-1">100%</div>
                <div className="text-white/80 text-sm">Satisfaction</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
              <Link
                to="/collections/all"
                className="inline-flex items-center px-6 py-3 bg-[#db8027] hover:bg-[#c4721f] text-white rounded-lg font-bold text-base transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
              >
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                </svg>
                Explore All Coffee
              </Link>


            </div>
          </div>
        </div>
      </div>

      <Analytics.ProductView
        data={{
          products: [
            {
              id: product.id,
              title: product.title,
              price: selectedVariant?.price.amount || '0',
              vendor: product.vendor,
              variantId: selectedVariant?.id || '',
              variantTitle: selectedVariant?.title || '',
              quantity: 1,
            },
          ],
        }}
      />
    </div>
  );
}

// Bundle Deals Component
function BundleDeals({allProducts}: {allProducts: any[]}) {
  const {open} = useAside();

  // Hidden product IDs that should not appear in bundle deals
  const hiddenProductIds = [
    'gid://shopify/Product/8469994373435', // Gold Rush Blend - Dark Roast
    'gid://shopify/Product/8469985231163', // Silver City Blend - Medium Roast
    'gid://shopify/Product/8296601387323', // Big River Blend - Medium Roast
  ];

  // Find actual bundle products from the store, excluding hidden products
  const bundleProducts = allProducts.filter(product =>
    product &&
    !hiddenProductIds.includes(product.id) &&
    (product.title.toLowerCase().includes('box') ||
     product.title.toLowerCase().includes('bundle') ||
     product.tags?.some((tag: string) => tag.toLowerCase().includes('bundle')))
  );

  // Get Roasters Box and Blend Box specifically
  const roastersBox = bundleProducts.find(product =>
    product.title.toLowerCase().includes('roasters') ||
    product.handle === 'roasters-box'
  );

  const blendBox = bundleProducts.find(product =>
    product.title.toLowerCase().includes('blend') &&
    product.title.toLowerCase().includes('box')
  );

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 animate-fade-in-up">
      {/* Roasters Box */}
      {roastersBox ? (
        <Link
          to={`/products/${roastersBox.handle}`}
          className="group bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 relative overflow-hidden hover:bg-white/15 hover:border-white/30 hover:shadow-2xl hover:shadow-white/10 transition-all duration-500 ease-out transform hover:-translate-y-2 hover:scale-[1.02] animate-slide-in-left cursor-pointer block"
        >
          <div className="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold transition-all duration-300 group-hover:scale-110 group-hover:bg-green-400">
            BEST VALUE
          </div>
          <div className="flex items-start space-x-4">
            {roastersBox.featuredImage && (
              <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 transition-transform duration-300 group-hover:scale-110">
                <Image
                  data={roastersBox.featuredImage}
                  aspectRatio="1/1"
                  sizes="64px"
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                />
              </div>
            )}
            <div className="flex-1">
              <h3 className="text-xl font-bold text-white mb-2">{roastersBox.title}</h3>
              <p className="text-white/90 mb-3 text-sm">3 different premium coffee varieties delivered monthly. Perfect for exploring new flavors!</p>

              <div className="space-y-2 mb-4">
                <div className="flex items-center space-x-2 text-white/80 text-sm">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>3 premium coffee bags per month</span>
                </div>
                <div className="flex items-center space-x-2 text-white/80 text-sm">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>Save 20% vs buying individually</span>
                </div>
                <div className="flex items-center space-x-2 text-white/80 text-sm">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>Premium quality guarantee</span>
                </div>
              </div>

              <div className="flex items-center justify-between mb-4">
                <div>
                  <div className="text-2xl font-bold text-white">
                    <Money data={roastersBox.priceRange.minVariantPrice} />
                  </div>
                  <div className="text-xs text-white/70">
                    ${(parseFloat(roastersBox.priceRange.minVariantPrice.amount) / 3).toFixed(2)} per bag
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-base font-bold text-green-400">Save 20%</div>
                  <div className="text-xs text-white/70">vs individual purchase</div>
                </div>
              </div>

              <div className="flex space-x-2" onClick={(e) => e.stopPropagation()}>
                <button
                  className="flex-1 bg-white text-[#db8027] font-bold py-3 px-4 rounded-lg text-center hover:bg-gray-100 hover:scale-105 hover:shadow-lg transition-all duration-300 text-sm transform active:scale-95"
                >
                  View Details
                </button>
                <AddToCartButton
                  disabled={!roastersBox.availableForSale}
                  onClick={() => open('cart')}
                  lines={
                    roastersBox.variants?.nodes?.[0]
                      ? [
                          {
                            merchandiseId: roastersBox.variants.nodes[0].id,
                            quantity: 1,
                            selectedVariant: roastersBox.variants.nodes[0],
                          },
                        ]
                      : []
                  }
                  className="flex-1 bg-army-600 hover:bg-army-700 hover:scale-105 hover:shadow-lg text-white font-bold py-3 px-4 rounded-lg transition-all duration-300 text-sm transform active:scale-95"
                >
                  Add to Cart
                </AddToCartButton>
              </div>
            </div>
          </div>
        </Link>
      ) : (
        // Fallback for Roasters Box
        <Link
          to="/collections/all"
          className="group bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 relative overflow-hidden hover:bg-white/15 hover:border-white/30 hover:shadow-2xl hover:shadow-white/10 transition-all duration-500 ease-out transform hover:-translate-y-2 hover:scale-[1.02] animate-slide-in-left cursor-pointer block"
        >
          <div className="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold transition-all duration-300 group-hover:scale-110 group-hover:bg-green-400">
            BEST VALUE
          </div>
          <div className="flex items-start space-x-4">
            <div className="w-16 h-16 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0 transition-transform duration-300 group-hover:scale-110">
              <span className="text-2xl transition-transform duration-300 group-hover:scale-110">📦</span>
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-bold text-white mb-2">Roasters Box</h3>
              <p className="text-white/90 mb-3 text-sm">3 different premium coffee varieties delivered monthly. Perfect for exploring new flavors!</p>
              <div onClick={(e) => e.stopPropagation()}>
                <button
                  className="w-full bg-white text-[#db8027] font-bold py-3 px-4 rounded-lg text-center hover:bg-gray-100 hover:scale-105 hover:shadow-lg transition-all duration-300 text-sm transform active:scale-95"
                >
                  Explore Bundles
                </button>
              </div>
            </div>
          </div>
        </Link>
      )}

      {/* Blend Box */}
      {blendBox ? (
        <Link
          to={`/products/${blendBox.handle}`}
          className="group bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 relative overflow-hidden hover:bg-white/15 hover:border-white/30 hover:shadow-2xl hover:shadow-white/10 transition-all duration-500 ease-out transform hover:-translate-y-2 hover:scale-[1.02] animate-slide-in-right cursor-pointer block"
        >
          <div className="absolute top-3 right-3 bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-bold transition-all duration-300 group-hover:scale-110 group-hover:bg-blue-400">
            POPULAR
          </div>
          <div className="flex items-start space-x-4">
            {blendBox.featuredImage && (
              <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 transition-transform duration-300 group-hover:scale-110">
                <Image
                  data={blendBox.featuredImage}
                  aspectRatio="1/1"
                  sizes="64px"
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                />
              </div>
            )}
            <div className="flex-1">
              <h3 className="text-xl font-bold text-white mb-2">{blendBox.title}</h3>
              <p className="text-white/90 mb-3 text-sm">Curated selection of our signature blends. Perfect for finding your favorite!</p>

              <div className="space-y-2 mb-4">
                <div className="flex items-center space-x-2 text-white/80 text-sm">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>2 signature blend bags</span>
                </div>
                <div className="flex items-center space-x-2 text-white/80 text-sm">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>Save 15% vs individual bags</span>
                </div>
                <div className="flex items-center space-x-2 text-white/80 text-sm">
                  <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>Perfect for gift giving</span>
                </div>
              </div>

              <div className="flex items-center justify-between mb-4">
                <div>
                  <div className="text-2xl font-bold text-white">
                    <Money data={blendBox.priceRange.minVariantPrice} />
                  </div>
                  <div className="text-xs text-white/70">
                    ${(parseFloat(blendBox.priceRange.minVariantPrice.amount) / 2).toFixed(2)} per bag
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-base font-bold text-green-400">Save 15%</div>
                  <div className="text-xs text-white/70">vs individual purchase</div>
                </div>
              </div>

              <div className="flex space-x-2" onClick={(e) => e.stopPropagation()}>
                <button
                  className="flex-1 bg-white text-[#db8027] font-bold py-3 px-4 rounded-lg text-center hover:bg-gray-100 hover:scale-105 hover:shadow-lg transition-all duration-300 text-sm transform active:scale-95"
                >
                  View Details
                </button>
                <AddToCartButton
                  disabled={!blendBox.availableForSale}
                  onClick={() => open('cart')}
                  lines={
                    blendBox.variants?.nodes?.[0]
                      ? [
                          {
                            merchandiseId: blendBox.variants.nodes[0].id,
                            quantity: 1,
                            selectedVariant: blendBox.variants.nodes[0],
                          },
                        ]
                      : []
                  }
                  className="flex-1 bg-army-600 hover:bg-army-700 hover:scale-105 hover:shadow-lg text-white font-bold py-3 px-4 rounded-lg transition-all duration-300 text-sm transform active:scale-95"
                >
                  Add to Cart
                </AddToCartButton>
              </div>
            </div>
          </div>
        </Link>
      ) : (
        // Fallback for Blend Box
        <Link
          to="/collections/all"
          className="group bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 relative overflow-hidden hover:bg-white/15 hover:border-white/30 hover:shadow-2xl hover:shadow-white/10 transition-all duration-500 ease-out transform hover:-translate-y-2 hover:scale-[1.02] animate-slide-in-right cursor-pointer block"
        >
          <div className="absolute top-3 right-3 bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-bold transition-all duration-300 group-hover:scale-110 group-hover:bg-blue-400">
            POPULAR
          </div>
          <div className="flex items-start space-x-4">
            <div className="w-16 h-16 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0 transition-transform duration-300 group-hover:scale-110">
              <span className="text-2xl transition-transform duration-300 group-hover:scale-110">🎯</span>
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-bold text-white mb-2">Blend Box</h3>
              <p className="text-white/90 mb-3 text-sm">Curated selection of our signature blends. Perfect for finding your favorite!</p>
              <div onClick={(e) => e.stopPropagation()}>
                <button
                  className="w-full bg-white text-[#db8027] font-bold py-3 px-4 rounded-lg text-center hover:bg-gray-100 hover:scale-105 hover:shadow-lg transition-all duration-300 text-sm transform active:scale-95"
                >
                  Explore Bundles
                </button>
              </div>
            </div>
          </div>
        </Link>
      )}
    </div>
  );
}

const PRODUCT_VARIANT_FRAGMENT = `#graphql
  fragment ProductVariant on ProductVariant {
    availableForSale
    compareAtPrice {
      amount
      currencyCode
    }
    id
    image {
      __typename
      id
      url
      altText
      width
      height
    }
    price {
      amount
      currencyCode
    }
    product {
      title
      handle
    }
    selectedOptions {
      name
      value
    }
    sku
    title
    unitPrice {
      amount
      currencyCode
    }
  }
` as const;

const PRODUCT_FRAGMENT = `#graphql
  fragment Product on Product {
    id
    title
    vendor
    handle
    descriptionHtml
    description
    encodedVariantExistence
    encodedVariantAvailability
    requiresSellingPlan
    images(first: 10) {
      nodes {
        id
        url
        altText
        width
        height
      }
    }
    sellingPlanGroups(first: 10) {
      nodes {
        name
        options {
          name
          values
        }
        sellingPlans(first: 10) {
          nodes {
            id
            name
            description
            options {
              name
              value
            }
            priceAdjustments {
              adjustmentValue {
                ... on SellingPlanFixedAmountPriceAdjustment {
                  adjustmentAmount {
                    amount
                    currencyCode
                  }
                }
                ... on SellingPlanFixedPriceAdjustment {
                  price {
                    amount
                    currencyCode
                  }
                }
                ... on SellingPlanPercentagePriceAdjustment {
                  adjustmentPercentage
                }
              }
              orderCount
            }
            recurringDeliveries
            checkoutCharge {
              type
              value {
                ... on SellingPlanCheckoutChargePercentageValue {
                  percentage
                }
                ... on MoneyV2 {
                  amount
                  currencyCode
                }
              }
            }
          }
        }
      }
    }
    options {
      name
      optionValues {
        name
        firstSelectableVariant {
          ...ProductVariant
        }
        swatch {
          color
          image {
            previewImage {
              url
            }
          }
        }
      }
    }
    selectedOrFirstAvailableVariant(selectedOptions: $selectedOptions, ignoreUnknownOptions: true, caseInsensitiveMatch: true) {
      ...ProductVariant
    }
    adjacentVariants (selectedOptions: $selectedOptions) {
      ...ProductVariant
    }
    seo {
      description
      title
    }
  }
  ${PRODUCT_VARIANT_FRAGMENT}
` as const;

const PRODUCT_QUERY = `#graphql
  query Product(
    $country: CountryCode
    $handle: String!
    $language: LanguageCode
    $selectedOptions: [SelectedOptionInput!]!
  ) @inContext(country: $country, language: $language) {
    product(handle: $handle) {
      ...Product
    }
  }
  ${PRODUCT_FRAGMENT}
` as const;

const RELATED_PRODUCTS_QUERY = `#graphql
  fragment RelatedProduct on Product {
    id
    title
    handle
    availableForSale
    tags
    description
    featuredImage {
      id
      url
      altText
      width
      height
    }
    priceRange {
      minVariantPrice {
        amount
        currencyCode
      }
      maxVariantPrice {
        amount
        currencyCode
      }
    }
    variants(first: 3) {
      nodes {
        id
        availableForSale
        selectedOptions {
          name
          value
        }
        price {
          amount
          currencyCode
        }
        title
        product {
          id
          handle
          title
          featuredImage {
            id
            url
            altText
            width
            height
          }
        }
      }
    }
  }
  query RelatedProducts(
    $country: CountryCode
    $language: LanguageCode
    $first: Int
  ) @inContext(country: $country, language: $language) {
    products(first: $first, sortKey: UPDATED_AT, reverse: true) {
      nodes {
        ...RelatedProduct
      }
    }
  }
` as const;
